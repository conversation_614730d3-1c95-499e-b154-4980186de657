function [data, info] = customMsg
%CustomMsg gives an empty data for my_custom_msgs/CustomMsg
% Copyright 2019-2021 The MathWorks, Inc.
data = struct();
data.MessageType = 'my_custom_msgs/CustomMsg';
[data.upperlimittrq, info.upperlimittrq] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.lowerlimittrq, info.lowerlimittrq] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.lowerlimitbrakepress, info.lowerlimitbrakepress] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.limitsactive, info.limitsactive] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.forwardcollisionwarning, info.forwardcollisionwarning] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.timetocollision, info.timetocollision] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.active, info.active] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.enable, info.enable] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
info.MessageType = 'my_custom_msgs/CustomMsg';
info.constant = 0;
info.default = 0;
info.maxstrlen = NaN;
info.MaxLen = 1;
info.MinLen = 1;
info.MatPath = cell(1,8);
info.MatPath{1} = 'upperlimittrq';
info.MatPath{2} = 'lowerlimittrq';
info.MatPath{3} = 'lowerlimitbrakepress';
info.MatPath{4} = 'limitsactive';
info.MatPath{5} = 'forwardcollisionwarning';
info.MatPath{6} = 'timetocollision';
info.MatPath{7} = 'active';
info.MatPath{8} = 'enable';
