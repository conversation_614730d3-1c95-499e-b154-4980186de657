function setup_mingw_env()
    % Setup MinGW environment for ROS2 message generation as alternative to Visual Studio
    
    % Check if MinGW is available
    mingw_paths = {
        'C:\mingw64\bin'
        'C:\msys64\mingw64\bin'
        'C:\TDM-GCC-64\bin'
        'C:\MinGW\bin'
    };
    
    mingw_found = false;
    for i = 1:length(mingw_paths)
        if exist(fullfile(mingw_paths{i}, 'gcc.exe'), 'file')
            mingw_path = mingw_paths{i};
            mingw_found = true;
            fprintf('Found MinGW at: %s\n', mingw_path);
            break;
        end
    end
    
    if ~mingw_found
        fprintf('MinGW not found. Please install MinGW-w64 or MSYS2.\n');
        fprintf('Download from: https://www.msys2.org/\n');
        return;
    end
    
    % Add MinGW to PATH
    current_path = getenv('PATH');
    if ~contains(current_path, mingw_path)
        new_path = [mingw_path ';' current_path];
        setenv('PATH', new_path);
        fprintf('Added MinGW to PATH\n');
    end
    
    % Set compiler environment variables for CMake
    setenv('CC', fullfile(mingw_path, 'gcc.exe'));
    setenv('CXX', fullfile(mingw_path, 'g++.exe'));
    setenv('CMAKE_GENERATOR', 'MinGW Makefiles');
    
    fprintf('MinGW environment configured for CMake.\n');
    fprintf('Note: You may need to install additional dependencies.\n');
end
