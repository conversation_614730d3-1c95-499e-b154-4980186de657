// Copyright 2020-2022 The MathWorks, Inc.
// Common copy functions for my_custom_msgs/CustomMsg
#ifdef _MSC_VER
#pragma warning(push)
#pragma warning(disable : 4100)
#pragma warning(disable : 4265)
#pragma warning(disable : 4456)
#pragma warning(disable : 4458)
#pragma warning(disable : 4946)
#pragma warning(disable : 4244)
#else
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wpedantic"
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#pragma GCC diagnostic ignored "-Wredundant-decls"
#pragma GCC diagnostic ignored "-Wnon-virtual-dtor"
#pragma GCC diagnostic ignored "-Wdelete-non-virtual-dtor"
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wshadow"
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif //_MSC_VER
#include "rclcpp/rclcpp.hpp"
#include "my_custom_msgs/msg/custom_msg.hpp"
#include "visibility_control.h"
#include "class_loader/multi_library_class_loader.hpp"
#include "ROS2PubSubTemplates.hpp"
class MY_CUSTOM_MSGS_EXPORT ros2_my_custom_msgs_msg_CustomMsg_common : public MATLABROS2MsgInterface<my_custom_msgs::msg::CustomMsg> {
  public:
    virtual ~ros2_my_custom_msgs_msg_CustomMsg_common(){}
    virtual void copy_from_struct(my_custom_msgs::msg::CustomMsg* msg, const matlab::data::Struct& arr, MultiLibLoader loader); 
    //----------------------------------------------------------------------------
    virtual MDArray_T get_arr(MDFactory_T& factory, const my_custom_msgs::msg::CustomMsg* msg, MultiLibLoader loader, size_t size = 1);
};
  void ros2_my_custom_msgs_msg_CustomMsg_common::copy_from_struct(my_custom_msgs::msg::CustomMsg* msg, const matlab::data::Struct& arr,
               MultiLibLoader loader) {
    try {
        //upperlimittrq
        const matlab::data::TypedArray<double> upperlimittrq_arr = arr["upperlimittrq"];
        msg->upperlimittrq = upperlimittrq_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'upperlimittrq' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'upperlimittrq' is wrong type; expected a double.");
    }
    try {
        //lowerlimittrq
        const matlab::data::TypedArray<double> lowerlimittrq_arr = arr["lowerlimittrq"];
        msg->lowerlimittrq = lowerlimittrq_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'lowerlimittrq' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'lowerlimittrq' is wrong type; expected a double.");
    }
    try {
        //lowerlimitbrakepress
        const matlab::data::TypedArray<double> lowerlimitbrakepress_arr = arr["lowerlimitbrakepress"];
        msg->lowerlimitbrakepress = lowerlimitbrakepress_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'lowerlimitbrakepress' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'lowerlimitbrakepress' is wrong type; expected a double.");
    }
    try {
        //limitsactive
        const matlab::data::TypedArray<double> limitsactive_arr = arr["limitsactive"];
        msg->limitsactive = limitsactive_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'limitsactive' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'limitsactive' is wrong type; expected a double.");
    }
    try {
        //forwardcollisionwarning
        const matlab::data::TypedArray<double> forwardcollisionwarning_arr = arr["forwardcollisionwarning"];
        msg->forwardcollisionwarning = forwardcollisionwarning_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'forwardcollisionwarning' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'forwardcollisionwarning' is wrong type; expected a double.");
    }
    try {
        //timetocollision
        const matlab::data::TypedArray<double> timetocollision_arr = arr["timetocollision"];
        msg->timetocollision = timetocollision_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'timetocollision' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'timetocollision' is wrong type; expected a double.");
    }
    try {
        //active
        const matlab::data::TypedArray<double> active_arr = arr["active"];
        msg->active = active_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'active' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'active' is wrong type; expected a double.");
    }
    try {
        //enable
        const matlab::data::TypedArray<double> enable_arr = arr["enable"];
        msg->enable = enable_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'enable' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'enable' is wrong type; expected a double.");
    }
  }
  //----------------------------------------------------------------------------
  MDArray_T ros2_my_custom_msgs_msg_CustomMsg_common::get_arr(MDFactory_T& factory, const my_custom_msgs::msg::CustomMsg* msg,
       MultiLibLoader loader, size_t size) {
    auto outArray = factory.createStructArray({size,1},{"MessageType","upperlimittrq","lowerlimittrq","lowerlimitbrakepress","limitsactive","forwardcollisionwarning","timetocollision","active","enable"});
    for(size_t ctr = 0; ctr < size; ctr++){
    outArray[ctr]["MessageType"] = factory.createCharArray("my_custom_msgs/CustomMsg");
    // upperlimittrq
    auto currentElement_upperlimittrq = (msg + ctr)->upperlimittrq;
    outArray[ctr]["upperlimittrq"] = factory.createScalar(currentElement_upperlimittrq);
    // lowerlimittrq
    auto currentElement_lowerlimittrq = (msg + ctr)->lowerlimittrq;
    outArray[ctr]["lowerlimittrq"] = factory.createScalar(currentElement_lowerlimittrq);
    // lowerlimitbrakepress
    auto currentElement_lowerlimitbrakepress = (msg + ctr)->lowerlimitbrakepress;
    outArray[ctr]["lowerlimitbrakepress"] = factory.createScalar(currentElement_lowerlimitbrakepress);
    // limitsactive
    auto currentElement_limitsactive = (msg + ctr)->limitsactive;
    outArray[ctr]["limitsactive"] = factory.createScalar(currentElement_limitsactive);
    // forwardcollisionwarning
    auto currentElement_forwardcollisionwarning = (msg + ctr)->forwardcollisionwarning;
    outArray[ctr]["forwardcollisionwarning"] = factory.createScalar(currentElement_forwardcollisionwarning);
    // timetocollision
    auto currentElement_timetocollision = (msg + ctr)->timetocollision;
    outArray[ctr]["timetocollision"] = factory.createScalar(currentElement_timetocollision);
    // active
    auto currentElement_active = (msg + ctr)->active;
    outArray[ctr]["active"] = factory.createScalar(currentElement_active);
    // enable
    auto currentElement_enable = (msg + ctr)->enable;
    outArray[ctr]["enable"] = factory.createScalar(currentElement_enable);
    }
    return std::move(outArray);
  } 
class MY_CUSTOM_MSGS_EXPORT ros2_my_custom_msgs_CustomMsg_message : public ROS2MsgElementInterfaceFactory {
  public:
    virtual ~ros2_my_custom_msgs_CustomMsg_message(){}
    virtual std::shared_ptr<MATLABPublisherInterface> generatePublisherInterface(ElementType /*type*/);
    virtual std::shared_ptr<MATLABSubscriberInterface> generateSubscriberInterface(ElementType /*type*/);
    virtual std::shared_ptr<void> generateCppMessage(ElementType /*type*/, const matlab::data::StructArray& /* arr */, MultiLibLoader /* loader */, std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* /*commonObjMap*/);
    virtual matlab::data::StructArray generateMLMessage(ElementType  /*type*/ ,void*  /* msg */, MultiLibLoader /* loader */ , std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* /*commonObjMap*/);
};  
  std::shared_ptr<MATLABPublisherInterface> 
          ros2_my_custom_msgs_CustomMsg_message::generatePublisherInterface(ElementType /*type*/){
    return std::make_shared<ROS2PublisherImpl<my_custom_msgs::msg::CustomMsg,ros2_my_custom_msgs_msg_CustomMsg_common>>();
  }
  std::shared_ptr<MATLABSubscriberInterface> 
         ros2_my_custom_msgs_CustomMsg_message::generateSubscriberInterface(ElementType /*type*/){
    return std::make_shared<ROS2SubscriberImpl<my_custom_msgs::msg::CustomMsg,ros2_my_custom_msgs_msg_CustomMsg_common>>();
  }
  std::shared_ptr<void> ros2_my_custom_msgs_CustomMsg_message::generateCppMessage(ElementType /*type*/, 
                                           const matlab::data::StructArray& arr,
                                           MultiLibLoader loader,
                                           std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* commonObjMap){
    auto msg = std::make_shared<my_custom_msgs::msg::CustomMsg>();
    ros2_my_custom_msgs_msg_CustomMsg_common commonObj;
    commonObj.mCommonObjMap = commonObjMap;
    commonObj.copy_from_struct(msg.get(), arr[0], loader);
    return msg;
  }
  matlab::data::StructArray ros2_my_custom_msgs_CustomMsg_message::generateMLMessage(ElementType  /*type*/ ,
                                                    void*  msg ,
                                                    MultiLibLoader  loader ,
                                                    std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>*  commonObjMap ){
    ros2_my_custom_msgs_msg_CustomMsg_common commonObj;	
    commonObj.mCommonObjMap = commonObjMap;	
    MDFactory_T factory;
    return commonObj.get_arr(factory, (my_custom_msgs::msg::CustomMsg*)msg, loader);			
 }
#include "class_loader/register_macro.hpp"
// Register the component with class_loader.
// This acts as a sort of entry point, allowing the component to be discoverable when its library
// is being loaded into a running process.
CLASS_LOADER_REGISTER_CLASS(ros2_my_custom_msgs_msg_CustomMsg_common, MATLABROS2MsgInterface<my_custom_msgs::msg::CustomMsg>)
CLASS_LOADER_REGISTER_CLASS(ros2_my_custom_msgs_CustomMsg_message, ROS2MsgElementInterfaceFactory)
#ifdef _MSC_VER
#pragma warning(pop)
#else
#pragma GCC diagnostic pop
#endif //_MSC_VER