ACLOCAL_PATH=C:\Program Files\Git\mingw64\share\aclocal;C:\Program Files\Git\usr\share\aclocal
ACSetupSvcPort=23210
ACSvcPort=17532
ALLUSERSPROFILE=C:\ProgramData
AME=C:\Program Files\Simcenter\2304\Amesim
APPDATA=C:\Users\<USER>\AppData\Roaming
CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_3368_WKYXCQAJKGDCXMSE
COLCON=1
COLORTERM=truecolor
COMMONPROGRAMFILES=C:\Program Files\Common Files
COMPUTERNAME=DESKTOP-3M25FA5
COMSPEC=C:\WINDOWS\system32\cmd.exe
CONFIG_SITE=C:/Program Files/Git/etc/config.site
CYCLONEDDS_URI=file://C:/Users/<USER>/.cyclonedds/cyclonedds.xml
ChocolateyInstall=C:\ProgramData\chocolatey
ChocolateyLastPathUpdate=134003645522405974
CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files
CommonProgramW6432=C:\Program Files\Common Files
DISPLAY=needs-to-be-defined
DriverData=C:\Windows\System32\Drivers\DriverData
EFC_30708_1592913036=1
EXEPATH=C:\Program Files\Git\bin
FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer
FPS_BROWSER_USER_PROFILE_STRING=Default
GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh
GIT_PAGER=cat
HOME=C:\Users\<USER>\Users\ASUS
HOSTNAME=DESKTOP-3M25FA5
INFOPATH=C:\Program Files\Git\mingw64\local\info;C:\Program Files\Git\mingw64\share\info;C:\Program Files\Git\usr\local\info;C:\Program Files\Git\usr\share\info;C:\Program Files\Git\usr\info;C:\Program Files\Git\share\info
LANG=en_US.UTF-8
LESS=-FX
LMS_LICENSE=5053@Localhost
LOCALAPPDATA=C:\Users\<USER>\AppData\Local
LOGONSERVER=\\DESKTOP-3M25FA5
MANPATH=C:\Program Files\Git\mingw64\local\man;C:\Program Files\Git\mingw64\share\man;C:\Program Files\Git\usr\local\man;C:\Program Files\Git\usr\share\man;C:\Program Files\Git\usr\man;C:\Program Files\Git\share\man
MATLAB=C:\Program Files\MATLAB\R2024a
MINGW_CHOST=x86_64-w64-mingw32
MINGW_PACKAGE_PREFIX=mingw-w64-x86_64
MINGW_PREFIX=C:/Program Files/Git/mingw64
MSYSTEM=MINGW64
MSYSTEM_CARCH=x86_64
MSYSTEM_CHOST=x86_64-w64-mingw32
MSYSTEM_PREFIX=C:/Program Files/Git/mingw64
NUMBER_OF_PROCESSORS=12
OLDPWD=D:/Works/matlab_ws
OPENSSL_CONF=C:\Program Files\OpenSSL-Win64\bin\openssl.cfg
ORIGINAL_PATH=C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Python38;C:\Python38\Scripts;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Simcenter\2304\Amesim;C:\Program Files\Graphviz\bin;C:\Program Files\Simcenter\2304\Amesim\win64;C:\Program Files\Simcenter\2304\Amesim\win32;C:\Program Files\Simcenter\2304\Amesim\sys\mingw32\bin;C:\Program Files\Simcenter\2304\Amesim\sys\python\win64;C:\dev\ros2_foxy\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\msys64\mingw64\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\MinGW\bin;C:\Program Files\Git\cmd;C:\Program Files\Cloudflare\Cloudflare WARP;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\ProgramData\chocolatey\lib\cunit\lib;C:\ProgramData\chocolatey\lib\tinyxml2\lib;C:\ProgramData\chocolatey\lib\log4cxx\lib;C:\ProgramData\chocolatey\lib\bullet\lib;C:\dev\ros2_foxy\Scripts;C:\dev\ros2_foxy\opt\libcurl_vendor\bin;C:\dev\ros2_foxy\opt\rviz_assimp_vendor\bin;C:\dev\ros2_foxy\opt\rviz_ogre_vendor\bin;C:\dev\ros2_foxy\opt\yaml_cpp_vendor\bin;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\MATLAB\R2023b\runtime\win64;C:\Program Files\MATLAB\R2023b\bin;C:\python38;C:\Python38\Scripts;C:\Program Files\OpenSSL-Win64\bin;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Graphviz\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Programs\Python\Python310-32\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310-32;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Google.Protobuf_Microsoft.Winget.Source_8wekyb3d8bbwe\bin;set PATH=C;C:\Program Files\Git\Program Files\MATLAB\MATLAB Runtime\R2024a\runtime\win6;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Python38;C:\Python38\Scripts;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Simcenter\2304\Amesim;C:\Program Files\Graphviz\bin;C:\Program Files\Simcenter\2304\Amesim\win64;C:\Program Files\Simcenter\2304\Amesim\win32;C:\Program Files\Simcenter\2304\Amesim\sys\mingw32\bin;C:\Program Files\Simcenter\2304\Amesim\sys\python\win64;C:\dev\ros2_foxy\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\msys64\mingw64\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\MinGW\bin;C:\Program Files\Git\cmd;C:\Program Files\Cloudflare\Cloudflare WARP;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\ProgramData\chocolatey\lib\cunit\lib;C:\ProgramData\chocolatey\lib\tinyxml2\lib;C:\ProgramData\chocolatey\lib\log4cxx\lib;C:\ProgramData\chocolatey\lib\bullet\lib;C:\dev\ros2_foxy\Scripts;C:\dev\ros2_foxy\opt\libcurl_vendor\bin;C:\dev\ros2_foxy\opt\rviz_assimp_vendor\bin;C:\dev\ros2_foxy\opt\rviz_ogre_vendor\bin;C:\dev\ros2_foxy\opt\yaml_cpp_vendor\bin;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\MATLAB\R2023b\runtime\win64;C:\Program Files\MATLAB\R2023b\bin;.
ORIGINAL_TEMP=C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_TMP=C:/Users/<USER>/AppData/Local/Temp
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
OS=Windows_NT
OneDrive=C:\Users\<USER>\OneDrive - Hanoi University of Science and Technology
OneDriveCommercial=C:\Users\<USER>\OneDrive - Hanoi University of Science and Technology
OneDriveConsumer=C:\Users\<USER>\OneDrive
OpenCV_DIR=C:\opencv
PAGER=cat
PATH=C:\Users\<USER>\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\local\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\usr\bin;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Python38;C:\Python38\Scripts;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Simcenter\2304\Amesim;C:\Program Files\Graphviz\bin;C:\Program Files\Simcenter\2304\Amesim\win64;C:\Program Files\Simcenter\2304\Amesim\win32;C:\Program Files\Simcenter\2304\Amesim\sys\mingw32\bin;C:\Program Files\Simcenter\2304\Amesim\sys\python\win64;C:\dev\ros2_foxy\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\msys64\mingw64\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\MinGW\bin;C:\Program Files\Git\cmd;C:\Program Files\Cloudflare\Cloudflare WARP;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\ProgramData\chocolatey\lib\cunit\lib;C:\ProgramData\chocolatey\lib\tinyxml2\lib;C:\ProgramData\chocolatey\lib\log4cxx\lib;C:\ProgramData\chocolatey\lib\bullet\lib;C:\dev\ros2_foxy\Scripts;C:\dev\ros2_foxy\opt\libcurl_vendor\bin;C:\dev\ros2_foxy\opt\rviz_assimp_vendor\bin;C:\dev\ros2_foxy\opt\rviz_ogre_vendor\bin;C:\dev\ros2_foxy\opt\yaml_cpp_vendor\bin;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\MATLAB\R2023b\runtime\win64;C:\Program Files\MATLAB\R2023b\bin;C:\python38;C:\Python38\Scripts;C:\Program Files\OpenSSL-Win64\bin;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Graphviz\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Users\<USER>\AppData\Local\Programs\Python\Python310-32\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python310-32;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\MinGW\bin;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Google.Protobuf_Microsoft.Winget.Source_8wekyb3d8bbwe\bin;set PATH=C;C:\Program Files\Git\Program Files\MATLAB\MATLAB Runtime\R2024a\runtime\win6;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;C:\Python38;C:\Python38\Scripts;C:\opencv\x64\vc16\bin;C:\Program Files\CMake\bin;C:\Program Files\Simcenter\2304\Amesim;C:\Program Files\Graphviz\bin;C:\Program Files\Simcenter\2304\Amesim\win64;C:\Program Files\Simcenter\2304\Amesim\win32;C:\Program Files\Simcenter\2304\Amesim\sys\mingw32\bin;C:\Program Files\Simcenter\2304\Amesim\sys\python\win64;C:\dev\ros2_foxy\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\msys64\mingw64\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit;C:\MinGW\bin;C:\Program Files\Git\cmd;C:\Program Files\Cloudflare\Cloudflare WARP;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\ProgramData\chocolatey\lib\cunit\lib;C:\ProgramData\chocolatey\lib\tinyxml2\lib;C:\ProgramData\chocolatey\lib\log4cxx\lib;C:\ProgramData\chocolatey\lib\bullet\lib;C:\dev\ros2_foxy\Scripts;C:\dev\ros2_foxy\opt\libcurl_vendor\bin;C:\dev\ros2_foxy\opt\rviz_assimp_vendor\bin;C:\dev\ros2_foxy\opt\rviz_ogre_vendor\bin;C:\dev\ros2_foxy\opt\yaml_cpp_vendor\bin;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files\MATLAB\R2023b\runtime\win64;C:\Program Files\MATLAB\R2023b\bin;.;C:\Program Files\Git\usr\bin\vendor_perl;C:\Program Files\Git\usr\bin\core_perl
PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW
PKG_CONFIG_PATH=C:\Program Files\Git\mingw64\lib\pkgconfig;C:\Program Files\Git\mingw64\share\pkgconfig
PKG_CONFIG_SYSTEM_INCLUDE_PATH=C:/Program Files/Git/mingw64/include
PKG_CONFIG_SYSTEM_LIBRARY_PATH=C:/Program Files/Git/mingw64/lib
PLINK_PROTOCOL=ssh
PROCESSOR_ARCHITECTURE=AMD64
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 68 Stepping 1, AuthenticAMD
PROCESSOR_LEVEL=25
PROCESSOR_REVISION=4401
PROGRAMFILES=C:\Program Files
PROMPT=$P$G
PS1=\[]633;A\]\[\033]0;$TITLEPREFIX:$PWD\007\]\n\[\033[32m\]\u@\h \[\033[35m\]$MSYSTEM \[\033[33m\]\w\[\033[36m\]`__git_ps1`\[\033[0m\]\n$ \[]633;B\]
PSModulePath=C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules
PUBLIC=C:\Users\<USER>\ProgramData
ProgramFiles(x86)=C:\Program Files (x86)
ProgramW6432=C:\Program Files
RMW_IMPLEMENTATION=rmw_cyclonedds_cpp
RlsSvcPort=22112
SC1D_LICENSING_TYPE=rlm
SESSIONNAME=Console
SHELL=C:\Program Files\Git\usr\bin\bash.exe
SHLVL=1
SSH_ASKPASS=C:/Program Files/Git/mingw64/bin/git-askpass.exe
SYSTEMDRIVE=C:
SYSTEMROOT=C:\WINDOWS
TEMP=C:\Users\<USER>\AppData\Local\Temp
TERM=xterm-256color
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=1.103.2
TMP=C:\Users\<USER>\AppData\Local\Temp
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
USERDOMAIN=DESKTOP-3M25FA5
USERDOMAIN_ROAMINGPROFILE=DESKTOP-3M25FA5
USERNAME=ASUS
USERPROFILE=C:\Users\<USER>\Users\ASUS\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js
VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe
VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-1c5a8ba399-sock
WINDIR=C:\WINDOWS
_=C:/Python38/Scripts/colcon
cmake=C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin
