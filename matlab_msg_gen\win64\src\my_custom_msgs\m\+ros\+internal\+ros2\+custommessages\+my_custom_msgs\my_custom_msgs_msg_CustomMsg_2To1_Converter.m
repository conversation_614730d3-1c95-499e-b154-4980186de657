function ros1msg = my_custom_msgs_msg_CustomMsg_2To1_Converter(message,ros1msg)
%my_custom_msgs_msg_CustomMsg_2To1_Converter passes data of ROS 2 message to ROS message.
% Copyright 2019 The MathWorks, Inc.    
ros1msg.Upperlimittrq = message.upperlimittrq;
ros1msg.Lowerlimittrq = message.lowerlimittrq;
ros1msg.Lowerlimitbrakepress = message.lowerlimitbrakepress;
ros1msg.Limitsactive = message.limitsactive;
ros1msg.Forwardcollisionwarning = message.forwardcollisionwarning;
ros1msg.Timetocollision = message.timetocollision;
ros1msg.Active = message.active;
ros1msg.Enable = message.enable;
end