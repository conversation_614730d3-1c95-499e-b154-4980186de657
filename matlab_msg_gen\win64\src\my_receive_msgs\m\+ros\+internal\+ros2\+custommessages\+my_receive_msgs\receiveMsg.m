function [data, info] = receiveMsg
%ReceiveMsg gives an empty data for my_receive_msgs/ReceiveMsg
% Copyright 2019-2021 The MathWorks, Inc.
data = struct();
data.MessageType = 'my_receive_msgs/ReceiveMsg';
[data.aebswitch, info.aebswitch] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.trafficobjectinscope, info.trafficobjectinscope] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.longtrafficdist, info.longtrafficdist] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.rellongtrafficspd, info.rellongtrafficspd] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.longspd, info.longspd] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.timetocollision, info.timetocollision] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
[data.maxmainbrakepress, info.maxmainbrakepress] = ros.internal.ros2.messages.ros2.default_type('double',1,0);
info.MessageType = 'my_receive_msgs/ReceiveMsg';
info.constant = 0;
info.default = 0;
info.maxstrlen = NaN;
info.MaxLen = 1;
info.MinLen = 1;
info.MatPath = cell(1,7);
info.MatPath{1} = 'aebswitch';
info.MatPath{2} = 'trafficobjectinscope';
info.MatPath{3} = 'longtrafficdist';
info.MatPath{4} = 'rellongtrafficspd';
info.MatPath{5} = 'longspd';
info.MatPath{6} = 'timetocollision';
info.MatPath{7} = 'maxmainbrakepress';
