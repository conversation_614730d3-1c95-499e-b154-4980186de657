// Copyright 2020-2022 The MathWorks, Inc.
// Common copy functions for my_receive_msgs/ReceiveMsg
#ifdef _MSC_VER
#pragma warning(push)
#pragma warning(disable : 4100)
#pragma warning(disable : 4265)
#pragma warning(disable : 4456)
#pragma warning(disable : 4458)
#pragma warning(disable : 4946)
#pragma warning(disable : 4244)
#else
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wpedantic"
#pragma GCC diagnostic ignored "-Wunused-local-typedefs"
#pragma GCC diagnostic ignored "-Wredundant-decls"
#pragma GCC diagnostic ignored "-Wnon-virtual-dtor"
#pragma GCC diagnostic ignored "-Wdelete-non-virtual-dtor"
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-Wshadow"
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif //_MSC_VER
#include "rclcpp/rclcpp.hpp"
#include "my_receive_msgs/msg/receive_msg.hpp"
#include "visibility_control.h"
#include "class_loader/multi_library_class_loader.hpp"
#include "ROS2PubSubTemplates.hpp"
class MY_RECEIVE_MSGS_EXPORT ros2_my_receive_msgs_msg_ReceiveMsg_common : public MATLABROS2MsgInterface<my_receive_msgs::msg::ReceiveMsg> {
  public:
    virtual ~ros2_my_receive_msgs_msg_ReceiveMsg_common(){}
    virtual void copy_from_struct(my_receive_msgs::msg::ReceiveMsg* msg, const matlab::data::Struct& arr, MultiLibLoader loader); 
    //----------------------------------------------------------------------------
    virtual MDArray_T get_arr(MDFactory_T& factory, const my_receive_msgs::msg::ReceiveMsg* msg, MultiLibLoader loader, size_t size = 1);
};
  void ros2_my_receive_msgs_msg_ReceiveMsg_common::copy_from_struct(my_receive_msgs::msg::ReceiveMsg* msg, const matlab::data::Struct& arr,
               MultiLibLoader loader) {
    try {
        //aebswitch
        const matlab::data::TypedArray<double> aebswitch_arr = arr["aebswitch"];
        msg->aebswitch = aebswitch_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'aebswitch' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'aebswitch' is wrong type; expected a double.");
    }
    try {
        //trafficobjectinscope
        const matlab::data::TypedArray<double> trafficobjectinscope_arr = arr["trafficobjectinscope"];
        msg->trafficobjectinscope = trafficobjectinscope_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'trafficobjectinscope' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'trafficobjectinscope' is wrong type; expected a double.");
    }
    try {
        //longtrafficdist
        const matlab::data::TypedArray<double> longtrafficdist_arr = arr["longtrafficdist"];
        msg->longtrafficdist = longtrafficdist_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'longtrafficdist' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'longtrafficdist' is wrong type; expected a double.");
    }
    try {
        //rellongtrafficspd
        const matlab::data::TypedArray<double> rellongtrafficspd_arr = arr["rellongtrafficspd"];
        msg->rellongtrafficspd = rellongtrafficspd_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'rellongtrafficspd' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'rellongtrafficspd' is wrong type; expected a double.");
    }
    try {
        //longspd
        const matlab::data::TypedArray<double> longspd_arr = arr["longspd"];
        msg->longspd = longspd_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'longspd' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'longspd' is wrong type; expected a double.");
    }
    try {
        //timetocollision
        const matlab::data::TypedArray<double> timetocollision_arr = arr["timetocollision"];
        msg->timetocollision = timetocollision_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'timetocollision' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'timetocollision' is wrong type; expected a double.");
    }
    try {
        //maxmainbrakepress
        const matlab::data::TypedArray<double> maxmainbrakepress_arr = arr["maxmainbrakepress"];
        msg->maxmainbrakepress = maxmainbrakepress_arr[0];
    } catch (matlab::data::InvalidFieldNameException&) {
        throw std::invalid_argument("Field 'maxmainbrakepress' is missing.");
    } catch (matlab::Exception&) {
        throw std::invalid_argument("Field 'maxmainbrakepress' is wrong type; expected a double.");
    }
  }
  //----------------------------------------------------------------------------
  MDArray_T ros2_my_receive_msgs_msg_ReceiveMsg_common::get_arr(MDFactory_T& factory, const my_receive_msgs::msg::ReceiveMsg* msg,
       MultiLibLoader loader, size_t size) {
    auto outArray = factory.createStructArray({size,1},{"MessageType","aebswitch","trafficobjectinscope","longtrafficdist","rellongtrafficspd","longspd","timetocollision","maxmainbrakepress"});
    for(size_t ctr = 0; ctr < size; ctr++){
    outArray[ctr]["MessageType"] = factory.createCharArray("my_receive_msgs/ReceiveMsg");
    // aebswitch
    auto currentElement_aebswitch = (msg + ctr)->aebswitch;
    outArray[ctr]["aebswitch"] = factory.createScalar(currentElement_aebswitch);
    // trafficobjectinscope
    auto currentElement_trafficobjectinscope = (msg + ctr)->trafficobjectinscope;
    outArray[ctr]["trafficobjectinscope"] = factory.createScalar(currentElement_trafficobjectinscope);
    // longtrafficdist
    auto currentElement_longtrafficdist = (msg + ctr)->longtrafficdist;
    outArray[ctr]["longtrafficdist"] = factory.createScalar(currentElement_longtrafficdist);
    // rellongtrafficspd
    auto currentElement_rellongtrafficspd = (msg + ctr)->rellongtrafficspd;
    outArray[ctr]["rellongtrafficspd"] = factory.createScalar(currentElement_rellongtrafficspd);
    // longspd
    auto currentElement_longspd = (msg + ctr)->longspd;
    outArray[ctr]["longspd"] = factory.createScalar(currentElement_longspd);
    // timetocollision
    auto currentElement_timetocollision = (msg + ctr)->timetocollision;
    outArray[ctr]["timetocollision"] = factory.createScalar(currentElement_timetocollision);
    // maxmainbrakepress
    auto currentElement_maxmainbrakepress = (msg + ctr)->maxmainbrakepress;
    outArray[ctr]["maxmainbrakepress"] = factory.createScalar(currentElement_maxmainbrakepress);
    }
    return std::move(outArray);
  } 
class MY_RECEIVE_MSGS_EXPORT ros2_my_receive_msgs_ReceiveMsg_message : public ROS2MsgElementInterfaceFactory {
  public:
    virtual ~ros2_my_receive_msgs_ReceiveMsg_message(){}
    virtual std::shared_ptr<MATLABPublisherInterface> generatePublisherInterface(ElementType /*type*/);
    virtual std::shared_ptr<MATLABSubscriberInterface> generateSubscriberInterface(ElementType /*type*/);
    virtual std::shared_ptr<void> generateCppMessage(ElementType /*type*/, const matlab::data::StructArray& /* arr */, MultiLibLoader /* loader */, std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* /*commonObjMap*/);
    virtual matlab::data::StructArray generateMLMessage(ElementType  /*type*/ ,void*  /* msg */, MultiLibLoader /* loader */ , std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* /*commonObjMap*/);
};  
  std::shared_ptr<MATLABPublisherInterface> 
          ros2_my_receive_msgs_ReceiveMsg_message::generatePublisherInterface(ElementType /*type*/){
    return std::make_shared<ROS2PublisherImpl<my_receive_msgs::msg::ReceiveMsg,ros2_my_receive_msgs_msg_ReceiveMsg_common>>();
  }
  std::shared_ptr<MATLABSubscriberInterface> 
         ros2_my_receive_msgs_ReceiveMsg_message::generateSubscriberInterface(ElementType /*type*/){
    return std::make_shared<ROS2SubscriberImpl<my_receive_msgs::msg::ReceiveMsg,ros2_my_receive_msgs_msg_ReceiveMsg_common>>();
  }
  std::shared_ptr<void> ros2_my_receive_msgs_ReceiveMsg_message::generateCppMessage(ElementType /*type*/, 
                                           const matlab::data::StructArray& arr,
                                           MultiLibLoader loader,
                                           std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>* commonObjMap){
    auto msg = std::make_shared<my_receive_msgs::msg::ReceiveMsg>();
    ros2_my_receive_msgs_msg_ReceiveMsg_common commonObj;
    commonObj.mCommonObjMap = commonObjMap;
    commonObj.copy_from_struct(msg.get(), arr[0], loader);
    return msg;
  }
  matlab::data::StructArray ros2_my_receive_msgs_ReceiveMsg_message::generateMLMessage(ElementType  /*type*/ ,
                                                    void*  msg ,
                                                    MultiLibLoader  loader ,
                                                    std::map<std::string,std::shared_ptr<MATLABROS2MsgInterfaceBase>>*  commonObjMap ){
    ros2_my_receive_msgs_msg_ReceiveMsg_common commonObj;	
    commonObj.mCommonObjMap = commonObjMap;	
    MDFactory_T factory;
    return commonObj.get_arr(factory, (my_receive_msgs::msg::ReceiveMsg*)msg, loader);			
 }
#include "class_loader/register_macro.hpp"
// Register the component with class_loader.
// This acts as a sort of entry point, allowing the component to be discoverable when its library
// is being loaded into a running process.
CLASS_LOADER_REGISTER_CLASS(ros2_my_receive_msgs_msg_ReceiveMsg_common, MATLABROS2MsgInterface<my_receive_msgs::msg::ReceiveMsg>)
CLASS_LOADER_REGISTER_CLASS(ros2_my_receive_msgs_ReceiveMsg_message, ROS2MsgElementInterfaceFactory)
#ifdef _MSC_VER
#pragma warning(pop)
#else
#pragma GCC diagnostic pop
#endif //_MSC_VER