function ros2msg = my_custom_msgs_msg_CustomMsg_1To2_Converter(message,ros2msg)
%my_custom_msgs_msg_CustomMsg_1To2_Converter passes data of ROS message to ROS 2 message.
% Copyright 2019 The MathWorks, Inc.
ros2msg.upperlimittrq = message.Upperlimittrq;
ros2msg.lowerlimittrq = message.Lowerlimittrq;
ros2msg.lowerlimitbrakepress = message.Lowerlimitbrakepress;
ros2msg.limitsactive = message.Limitsactive;
ros2msg.forwardcollisionwarning = message.Forwardcollisionwarning;
ros2msg.timetocollision = message.Timetocollision;
ros2msg.active = message.Active;
ros2msg.enable = message.Enable;
end