[0.000000] (-) TimerEvent: {}
[0.000000] (my_custom_msgs) JobQueued: {'identifier': 'my_custom_msgs', 'dependencies': OrderedDict()}
[0.000000] (my_receive_msgs) JobQueued: {'identifier': 'my_receive_msgs', 'dependencies': OrderedDict()}
[0.000000] (my_custom_msgs) JobStarted: {'identifier': 'my_custom_msgs'}
[0.016000] (my_receive_msgs) JobStarted: {'identifier': 'my_receive_msgs'}
[0.062000] (my_receive_msgs) JobProgress: {'identifier': 'my_receive_msgs', 'progress': 'cmake'}
[0.094000] (my_receive_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n  File "C:\\Python38\\lib\\site-packages\\colcon_core\\executor\\__init__.py", line 91, in __call__\n    rc = await self.task(*args, **kwargs)\n  File "C:\\Python38\\lib\\site-packages\\colcon_core\\task\\__init__.py", line 93, in __call__\n    return await task_method(*args, **kwargs)\n  File "C:\\Python38\\lib\\site-packages\\colcon_ros\\task\\ament_cmake\\build.py", line 59, in build\n    rc = await extension.build(\n  File "C:\\Python38\\lib\\site-packages\\colcon_cmake\\task\\cmake\\build.py", line 87, in build\n    rc = await self._reconfigure(args, env)\n  File "C:\\Python38\\lib\\site-packages\\colcon_cmake\\task\\cmake\\build.py", line 151, in _reconfigure\n    raise RuntimeError(\nRuntimeError: VisualStudioVersion is not set, please run within a Visual Studio Command Prompt.\n'}
[0.094000] (my_receive_msgs) JobEnded: {'identifier': 'my_receive_msgs', 'rc': 1}
[0.094000] (my_custom_msgs) JobProgress: {'identifier': 'my_custom_msgs', 'progress': 'cmake'}
[0.094000] (my_custom_msgs) StderrLine: {'line': b'Traceback (most recent call last):\n  File "C:\\Python38\\lib\\site-packages\\colcon_core\\executor\\__init__.py", line 91, in __call__\n    rc = await self.task(*args, **kwargs)\n  File "C:\\Python38\\lib\\site-packages\\colcon_core\\task\\__init__.py", line 93, in __call__\n    return await task_method(*args, **kwargs)\n  File "C:\\Python38\\lib\\site-packages\\colcon_ros\\task\\ament_cmake\\build.py", line 59, in build\n    rc = await extension.build(\n  File "C:\\Python38\\lib\\site-packages\\colcon_cmake\\task\\cmake\\build.py", line 87, in build\n    rc = await self._reconfigure(args, env)\n  File "C:\\Python38\\lib\\site-packages\\colcon_cmake\\task\\cmake\\build.py", line 151, in _reconfigure\n    raise RuntimeError(\nRuntimeError: VisualStudioVersion is not set, please run within a Visual Studio Command Prompt.\n'}
[0.094000] (my_custom_msgs) JobEnded: {'identifier': 'my_custom_msgs', 'rc': 1}
[0.109000] (-) TimerEvent: {}
[0.109000] (-) EventReactorShutdown: {}
