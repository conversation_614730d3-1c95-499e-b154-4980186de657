function setup_vs_env()
    % Setup Visual Studio environment for ROS2 message generation
    
    % Common Visual Studio installation paths
    vs_paths = {
        'C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvars64.bat'
        'C:\Program Files (x86)\Microsoft Visual Studio\2017\Enterprise\VC\Auxiliary\Build\vcvars64.bat'
    };
    
    % Find existing Visual Studio installation
    vs_bat_file = '';
    for i = 1:length(vs_paths)
        if exist(vs_paths{i}, 'file')
            vs_bat_file = vs_paths{i};
            break;
        end
    end
    
    if isempty(vs_bat_file)
        error('Visual Studio not found. Please install Visual Studio with C++ tools.');
    end
    
    fprintf('Found Visual Studio at: %s\n', vs_bat_file);
    
    % Get environment variables from vcvars64.bat
    [status, result] = system(sprintf('"%s" && set', vs_bat_file));
    
    if status == 0
        % Parse environment variables
        lines = strsplit(result, '\n');
        for i = 1:length(lines)
            line = strtrim(lines{i});
            if contains(line, '=') && ~startsWith(line, 'PROMPT=')
                parts = strsplit(line, '=', 2);
                if length(parts) == 2
                    var_name = parts{1};
                    var_value = parts{2};
                    
                    % Set important Visual Studio environment variables
                    if any(strcmp(var_name, {'VisualStudioVersion', 'VCINSTALLDIR', ...
                            'WindowsSDKVersion', 'PATH', 'INCLUDE', 'LIB', 'LIBPATH'}))
                        setenv(var_name, var_value);
                        if strcmp(var_name, 'VisualStudioVersion')
                            fprintf('Set VisualStudioVersion = %s\n', var_value);
                        end
                    end
                end
            end
        end
        fprintf('Visual Studio environment configured successfully.\n');
    else
        error('Failed to configure Visual Studio environment.');
    end
end
