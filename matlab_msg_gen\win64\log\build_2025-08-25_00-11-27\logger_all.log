[1.476s] [34mcolcon[0m [1;30mDEBUG[0m [32mCommand line arguments: ['C:\\Python38\\Scripts\\colcon', 'build', '--merge-install'][0m
[1.476s] [34mcolcon[0m [1;30mDEBUG[0m [32mParsed command line arguments: Namespace(ament_cmake_args=None, base_paths=['.'], build_base='build', catkin_cmake_args=None, catkin_skip_building_tests=False, cmake_args=None, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, cmake_target=None, cmake_target_skip_unavailable=False, continue_on_error=False, event_handlers=None, executor='parallel', ignore_user_meta=False, install_base='install', log_base=None, log_level=None, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x00000276CB0BECD0>>, merge_install=True, metas=['./colcon.meta'], packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_end=None, packages_ignore=None, packages_ignore_regex=None, packages_select=None, packages_select_build_failed=False, packages_select_by_dep=None, packages_select_regex=None, packages_select_test_failures=False, packages_skip=None, packages_skip_build_finished=False, packages_skip_by_dep=None, packages_skip_regex=None, packages_skip_test_passed=False, packages_skip_up_to=None, packages_start=None, packages_up_to=None, packages_up_to_regex=None, parallel_workers=12, paths=None, symlink_install=False, test_result_base=None, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x00000276CB0BECD0>, verb_name='build', verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x00000276CB1FB7C0>)[0m
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) check parameters
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) check parameters
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) check parameters
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) check parameters
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(colcon_meta) discover
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) discover
[1.636s] [34mcolcon.colcon_core.package_discovery[0m [1;30mINFO[0m Crawling recursively for packages in 'D:\Works\matlab_ws\matlab_msg_gen\win64'
[1.636s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ignore', 'ignore_ament_install']
[1.636s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore'
[1.636s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ignore_ament_install'
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_pkg']
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_pkg'
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['colcon_meta']
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'colcon_meta'
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['ros']
[1.637s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'ros'
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['cmake', 'python']
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'cmake'
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python'
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extensions ['python_setup_py']
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(.) by extension 'python_setup_py'
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extensions ['ignore', 'ignore_ament_install']
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) by extension 'ignore'
[1.698s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(build) ignored
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extensions ['ignore', 'ignore_ament_install']
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) by extension 'ignore'
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(install) ignored
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extensions ['ignore', 'ignore_ament_install']
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) by extension 'ignore'
[1.699s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(log) ignored
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ignore', 'ignore_ament_install']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ignore_ament_install'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_pkg']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_pkg'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['colcon_meta']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'colcon_meta'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['ros']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'ros'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['cmake', 'python']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'cmake'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python'
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extensions ['python_setup_py']
[1.700s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src) by extension 'python_setup_py'
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extension 'ignore'
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extension 'ignore_ament_install'
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extensions ['colcon_pkg']
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extension 'colcon_pkg'
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extensions ['colcon_meta']
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extension 'colcon_meta'
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extensions ['ros']
[1.701s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_custom_msgs) by extension 'ros'
[1.707s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src\my_custom_msgs' with type 'ros.ament_cmake' and name 'my_custom_msgs'[0m
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extensions ['ignore', 'ignore_ament_install']
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extension 'ignore'
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extension 'ignore_ament_install'
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extensions ['colcon_pkg']
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extension 'colcon_pkg'
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extensions ['colcon_meta']
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extension 'colcon_meta'
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extensions ['ros']
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mLevel 1[0m _identify(src\my_receive_msgs) by extension 'ros'
[1.708s] [34mcolcon.colcon_core.package_identification[0m [1;30mDEBUG[0m [32mPackage 'src\my_receive_msgs' with type 'ros.ament_cmake' and name 'my_receive_msgs'[0m
[1.708s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(recursive) using defaults
[1.708s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) discover
[1.708s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(ignore) using defaults
[1.708s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) discover
[1.708s] [34mcolcon.colcon_core.package_discovery[0m [1;30mLevel 1[0m discover_packages(path) using defaults
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_target' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_clean_first' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'cmake_force_configure' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'ament_cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_custom_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mDEBUG[0m [32mBuilding package 'my_custom_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\build\\my_custom_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\install', 'merge_install': True, 'path': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\src\\my_custom_msgs', 'symlink_install': False, 'test_result_base': None}[0m
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_target' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_clean_cache' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_clean_first' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'cmake_force_configure' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'ament_cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'catkin_cmake_args' from command line to 'None'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mLevel 5[0m set package 'my_receive_msgs' build argument 'catkin_skip_building_tests' from command line to 'False'
[1.744s] [34mcolcon.colcon_core.verb[0m [1;30mDEBUG[0m [32mBuilding package 'my_receive_msgs' with the following arguments: {'ament_cmake_args': None, 'build_base': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\build\\my_receive_msgs', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\install', 'merge_install': True, 'path': 'D:\\Works\\matlab_ws\\matlab_msg_gen\\win64\\src\\my_receive_msgs', 'symlink_install': False, 'test_result_base': None}[0m
[1.744s] [34mcolcon.colcon_core.executor[0m [1;30mINFO[0m Executing jobs using 'parallel' executor
[1.744s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete[0m
[1.744s] [34mcolcon.colcon_ros.task.ament_cmake.build[0m [1;30mINFO[0m Building ROS package in 'D:\Works\matlab_ws\matlab_msg_gen\win64\src\my_custom_msgs' with build type 'ament_cmake'
[1.747s] [34mcolcon.colcon_cmake.task.cmake.build[0m [1;30mINFO[0m Building CMake package in 'D:\Works\matlab_ws\matlab_msg_gen\win64\src\my_custom_msgs'
[1.750s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_core.shell.sh': Not used on Windows systems
[1.750s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.758s] [34mcolcon.colcon_ros.task.ament_cmake.build[0m [1;30mINFO[0m Building ROS package in 'D:\Works\matlab_ws\matlab_msg_gen\win64\src\my_receive_msgs' with build type 'ament_cmake'
[1.758s] [34mcolcon.colcon_cmake.task.cmake.build[0m [1;30mINFO[0m Building CMake package in 'D:\Works\matlab_ws\matlab_msg_gen\win64\src\my_receive_msgs'
[1.758s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.854s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mclosing loop[0m
[1.854s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mloop closed[0m
[1.855s] [34mcolcon.colcon_parallel_executor.executor.parallel[0m [1;30mDEBUG[0m [32mrun_until_complete finished with 'VisualStudioVersion is not set, please run within a Visual Studio Command Prompt.'[0m
[1.855s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoining thread[0m
[1.861s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[1.861s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.notify_send': Not used on non-Linux systems
[1.861s] [34mcolcon.colcon_core.plugin_system[0m [1;30mINFO[0m Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[1.861s] [34mcolcon.colcon_notification.desktop_notification[0m [1;30mINFO[0m Sending desktop notification using 'win32'
[6.988s] [34mcolcon.colcon_core.event_reactor[0m [1;30mDEBUG[0m [32mjoined thread[0m
[6.988s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\local_setup.ps1'
[7.004s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\_local_setup_util_ps1.py'
[7.016s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\setup.ps1'
[7.028s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix script 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\local_setup.bat'
[7.049s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix util module 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\_local_setup_util_bat.py'
[7.051s] [34mcolcon.colcon_core.shell[0m [1;30mINFO[0m Creating prefix chain script 'D:\Works\matlab_ws\matlab_msg_gen\win64\install\setup.bat'
